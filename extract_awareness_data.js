const fs = require('fs');

function extractQuestionsFromAwarenessHTML() {
    try {
        const html = fs.readFileSync('awareness.html', 'utf8');
        
        // Split by question containers
        const questionSections = html.split('class="step ays_question_result"');
        
        const questions = [];
        
        for (let i = 1; i < questionSections.length; i++) {
            const section = questionSections[i];
            
            // Extract question number from counter
            const counterMatch = section.match(/(\d+)\s*\/\s*18/);
            if (!counterMatch) continue;
            
            const questionNum = parseInt(counterMatch[1]);
            
            // Extract question text
            const questionMatch = section.match(/<p>([^<]+)<\/p>/);
            if (!questionMatch) continue;
            
            const questionText = questionMatch[1].trim().replace(/&nbsp;/g, ' ');
            
            // Extract image if exists
            const imageMatch = section.match(/src="([^"]+)"/);
            const imageUrl = imageMatch ? imageMatch[1] : null;
            
            // Extract all options
            const options = [];
            const optionMatches = [...section.matchAll(/<label[^>]*class="[^"]*ays_position_initial[^"]*"[^>]*>([^<]+)<\/label>/g)];
            
            for (let optionMatch of optionMatches) {
                const optionText = optionMatch[1].trim().replace(/&nbsp;/g, ' ');
                if (optionText && optionText.length > 3) {
                    options.push(optionText);
                }
            }
            
            // Find correct answer
            let correctAnswerIndex = 0;
            const correctMatches = [...section.matchAll(/<label[^>]*class="[^"]*correct[^"]*"[^>]*>([^<]+)<\/label>/g)];
            
            if (correctMatches.length > 0) {
                const correctText = correctMatches[0][1].trim().replace(/&nbsp;/g, ' ');
                for (let j = 0; j < options.length; j++) {
                    if (options[j] === correctText) {
                        correctAnswerIndex = j;
                        break;
                    }
                }
            }
            
            // Build question object
            if (options.length >= 2 && questionText.length > 10) {
                const questionObj = {
                    numbers: `ข้อที่ ${questionNum} :`,
                    question: [{ text: questionText }],
                    options: options.map(opt => ({ text: opt })),
                    correctAnswer: correctAnswerIndex,
                    order: ['text']
                };
                
                if (imageUrl) {
                    questionObj.question.push({ image: imageUrl });
                    questionObj.order = ['text', 'image'];
                }
                
                questions.push(questionObj);
            }
        }
        
        // Sort by question number
        questions.sort((a, b) => {
            const numA = parseInt(a.numbers.match(/\d+/)[0]);
            const numB = parseInt(b.numbers.match(/\d+/)[0]);
            return numA - numB;
        });
        
        return questions;
        
    } catch (error) {
        console.error('Error:', error.message);
        return [];
    }
}

function generateTypeScriptFileAwareness(questions, outputFile) {
    const tsContent = `export type Option = {
  text?: string;
  image?: string;
};

export type Question = {
  numbers: string;
  question: Option[];
  options: Option[]; // options can be either text or an image object
  correctAnswer: number; // Index of the correct answer
  order: ("text" | "image")[];
};

export const quizData: Question[] = [
${questions.map((question, i) => {
    let result = '  {\n';
    result += `    numbers: "${question.numbers}",\n`;
    result += '    question: [\n';
    for (let q of question.question) {
        if (q.text) {
            result += `      { text: "${q.text.replace(/"/g, '\\"')}" },\n`;
        } else if (q.image) {
            result += `      { image: "${q.image}" },\n`;
        }
    }
    result += '    ],\n';
    result += '    options: [\n';
    for (let opt of question.options) {
        result += `      { text: "${opt.text.replace(/"/g, '\\"')}" },\n`;
    }
    result += '    ],\n';
    result += `    correctAnswer: ${question.correctAnswer},\n`;
    result += `    order: ${JSON.stringify(question.order)},\n`;
    result += '  }';
    if (i < questions.length - 1) {
        result += ',';
    }
    return result;
}).join('\n')}
];
`;
    
    fs.writeFileSync(outputFile, tsContent);
}

// Main execution
const questions = extractQuestionsFromAwarenessHTML();
console.log(`Extracted ${questions.length} questions from awareness.html`);

if (questions.length > 0) {
    generateTypeScriptFileAwareness(questions, 'awareness.ts');
    console.log('Generated awareness.ts successfully!');
    
    // Print summary
    console.log('\nSummary:');
    console.log(`Total questions: ${questions.length}`);
    
    const questionsWithImages = questions.filter(q => q.order.includes('image'));
    console.log(`Questions with images: ${questionsWithImages.length}`);
    
    console.log('\nFirst 5 questions:');
    questions.slice(0, 5).forEach(q => {
        console.log(`${q.numbers} ${q.question[0].text}`);
    });
    
    if (questionsWithImages.length > 0) {
        console.log('\nQuestions with images:');
        questionsWithImages.slice(0, 5).forEach(q => {
            console.log(`${q.numbers} (has image)`);
        });
    }
} else {
    console.log('No questions found!');
}
