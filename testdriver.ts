export type Option = {
  text?: string;
  image?: string;
};

export type Question = {
  numbers: string;
  question: Option[];
  options: Option[]; // options can be either text or an image object
  correctAnswer: number; // Index of the correct answer
  order: ("text" | "image")[];
};

export const quizData: Question[] = [
  {
    numbers: "ข้อที่ 41 :",
    question: [
      { text: "การขับรถในช่วงหน้าฝนควรตรวจเช็คอุปกรณ์ส่วนควบอะไรบ้างเพื่อความปลอดภัย" },
    ],
    options: [
      { text: "ที่ปัดน้ำฝน" },
      { text: "น้ำมันเพาเวอร์" },
      { text: "น้ำมันเฟืองท้าย" },
      { text: "น้ำมันเกียร์" },
    ],
    correctAnswer: 0,
    order: ["text"],
  },
  {
    numbers: "ข้อที่ 42 :",
    question: [
      { text: "การถอยหลังชิดขอบทางล้อหน้าควรอยู่ลักษณะใด ?" },
    ],
    options: [
      { text: "ล้อหน้าเฉียงออกทางด้านซ้าย" },
      { text: "ตรงและขนานกับขอบทางหรือฟุตบาต" },
      { text: "หันเข้าหาขอบทาง" },
      { text: "หันออกจากขอบทาง" },
    ],
    correctAnswer: 1,
    order: ["text"],
  },
  {
    numbers: "ข้อที่ 43 :",
    question: [
      { text: "เมื่อพบเครื่องหมายนี้ ผู้ขับขี่ต้องปฏิบัติอย่างไร" },
      { image: "https://lh7-rt.googleusercontent.com/formsz/AN7BsVBFt_XMNeRa9KM23Rg4o21pF2oECXenakdbvcP6N8EiSbz_doALwpD7_Aa5BEyFLvX0yR1-56NQqp3GJAWWpqyJKjTl-x0T9IQUmjiKw2_3IX8m1LI20h0eum4wrHY3k47LesItcFodJ_wUNEep2Z7E=w199?key=LJLYe3PkZlCT_te3OepCCg" },
    ],
    options: [
      { text: "ให้ขับรถช้าลง และระมัดระวัง คนงานกำลังสำรวจสิ่งก่อสร้าง" },
      { text: "ให้ขับรถช้าลง และระมัดระวัง เครื่องจักรกำลังทำงาน" },
      { text: "ให้ขับรถช้าลง และระมัดระวัง ข้างหน้ามีงานสำรวจทาง" },
      { text: "ให้ขับรถช้าลง และระมัดระวัง คนงานกำลังทำงาน" },
    ],
    correctAnswer: 3,
    order: ["text", "image"],
  },
  {
    numbers: "ข้อที่ 44 :",
    question: [
      { text: "เครื่องหมายขอบทางสีขาว – แดง หมายความว่าอย่างไร" },
      { image: "https://lh7-rt.googleusercontent.com/formsz/AN7BsVCTAPHF_m0hFKEZWfjUmGU_lbu30HJAjfU_AguAC2rHjuQ_UjaSQkuBschklGCdqUwzWAbl1Ff1DFMtf3m1U1PQ4Eqvj9GbN80bXMYmk_mNbtEV9lnpn9V_eNMSCy2eNU3TT9WbDsQDDNR113o8K7au=w200?key=LJLYe3PkZlCT_te3OepCCg" },
    ],
    options: [
      { text: "ห้ามหยุดแต่สามารถจอดรถได้ทุกชนิด" },
      { text: "ห้ามหยุด ห้ามจอดรถทุกชนิด" },
      { text: "ห้ามจอดรถทุกชนิดยกเว้นรถยนต์" },
      { text: "ห้ามจอดแต่สามารถหยุดรถได้ชั่วขณะ" },
    ],
    correctAnswer: 1,
    order: ["text", "image"],
  },
  {
    numbers: "ข้อที่ 45 :",
    question: [
      { text: "ห้ามมิให้ผู้ขับขี่รถแซงเพื่อขึ้นหน้ารถค้นอื่นขณะที่มีหมอก ฝุ่น ฝน หรือควัน จนไม่อาจเห็นทางข้างหน้าได้ในระยะเท่าใด" },
    ],
    options: [
      { text: "90 เมตร" },
      { text: "70 เมตร" },
      { text: "80 เมตร" },
      { text: "60 เมตร" },
    ],
    correctAnswer: 3,
    order: ["text"],
  },
  {
    numbers: "ข้อที่ 46 :",
    question: [
      { text: "ในขณะขับรถยนต์ถ้ามาตรวัดความร้อนสูงขึ้น จนผิดปกติเราควรตรวจดูที่จุดใด" },
    ],
    options: [
      { text: "ถังน้ำมันเชื้อเพลิง" },
      { text: "ระดับน้ำมันเครื่อง" },
      { text: "ระดับน้ำในหม้อน้ำหรือในหม้อพักน้ำ" },
      { text: "ระดับน้ำมันเบรก" },
    ],
    correctAnswer: 2,
    order: ["text"],
  },
  {
    numbers: "ข้อที่ 47 :",
    question: [
      { text: "เมื่อขับรถแซงขึ้นหน้าแล้วจะเข้าช่องเดินรถด้านซ้ายต้องปฏิบัติอย่างไร" },
    ],
    options: [
      { text: "มองกระจกด้านซ้าย เห็นรถคันที่ถูกแซงอยู่ในระยะที่ปลอดภัย" },
      { text: "มองในกระจกด้านขวา" },
      { text: "มองกระจกหลัง เห็นรถคันที่ถูกแซงอยู่ในระยะที่ปลอดภัย" },
      { text: "มองกระจกข้าง เห็นรถคันที่ถูกแซงอยู่ในระยะที่ปลอดภัย" },
    ],
    correctAnswer: 2,
    order: ["text"],
  },
];
