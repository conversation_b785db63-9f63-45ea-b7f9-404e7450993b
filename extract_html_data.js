const fs = require('fs');

function extractQuestionsFromHTML() {
    const html = fs.readFileSync('testdriver.html', 'utf8');

    // Extract all questions using multiple patterns
    const questions = [];

    // Pattern 1: Extract questions with M7eMe class
    const questionPattern1 = /M7eMe">(\d+)\.\s*([^<]+?)(?=<\/span>)/g;
    let match;

    while ((match = questionPattern1.exec(html)) !== null) {
        const questionNum = parseInt(match[1]);
        const questionText = match[2].trim();

        if (questionText.length > 10) {
            questions.push({
                number: questionNum,
                text: questionText,
                rawSection: null
            });
        }
    }

    // Split by question containers to get full sections
    const questionSections = html.split('role="listitem"');

    for (let section of questionSections) {
        // Skip email section and header sections
        if (section.includes('อีเมล') || section.includes('แบบทดสอบ') || section.length < 100) {
            continue;
        }

        // Extract question number from section
        const questionMatch = section.match(/(\d+)\.\s*([^<]+?)(?=<\/span>)/);
        if (!questionMatch) continue;

        const questionNum = parseInt(questionMatch[1]);

        // Find corresponding question in our array
        const questionIndex = questions.findIndex(q => q.number === questionNum);
        if (questionIndex !== -1) {
            questions[questionIndex].rawSection = section;
        }
    }

    // Process each question to extract options and answers
    const processedQuestions = [];

    for (let questionData of questions) {
        if (!questionData.rawSection) continue;

        const section = questionData.rawSection;

        // Extract image if exists
        const imageMatch = section.match(/src="([^"]+)"/);
        const imageUrl = imageMatch ? imageMatch[1] : null;

        // Extract all options using multiple patterns
        const options = [];

        // Pattern 1: aria-label
        const optionMatches1 = [...section.matchAll(/aria-label="([^"]+)"/g)];
        for (let optionMatch of optionMatches1) {
            const optionText = optionMatch[1].trim();
            if (!optionText.includes('ไม่ถูกต้อง') &&
                !optionText.includes('คำถามที่ต้องตอบ') &&
                !optionText.includes('คะแนนที่ได้รับ') &&
                !optionText.includes('role=') &&
                !optionText.includes('aria-') &&
                optionText.length > 5) {
                options.push(optionText);
            }
        }

        // Pattern 2: aDTYNe class
        const optionMatches2 = [...section.matchAll(/aDTYNe snByac kTYmRb OIC90c">([^<]+)/g)];
        for (let optionMatch of optionMatches2) {
            const optionText = optionMatch[1].trim();
            if (!optionText.includes('คำตอบที่ถูกต้อง') &&
                !options.includes(optionText) &&
                optionText.length > 3) {
                options.push(optionText);
            }
        }

        // Find correct answer
        const correctAnswerSection = section.split('คำตอบที่ถูกต้อง')[1];
        let correctAnswerIndex = 0;

        if (correctAnswerSection) {
            const correctMatch = correctAnswerSection.match(/aria-label="([^"]+)"/);
            if (correctMatch) {
                const correctText = correctMatch[1].trim();
                for (let i = 0; i < options.length; i++) {
                    if (options[i].includes(correctText) || correctText.includes(options[i])) {
                        correctAnswerIndex = i;
                        break;
                    }
                }
            }
        }

        // Remove duplicates and limit to 4 options
        const uniqueOptions = [...new Set(options)].slice(0, 4);

        if (uniqueOptions.length >= 2) {
            const questionObj = {
                numbers: `ข้อที่ ${questionData.number} :`,
                question: [{ text: questionData.text }],
                options: uniqueOptions.map(opt => ({ text: opt })),
                correctAnswer: Math.min(correctAnswerIndex, uniqueOptions.length - 1),
                order: ['text']
            };

            if (imageUrl) {
                questionObj.question.push({ image: imageUrl });
                questionObj.order = ['text', 'image'];
            }

            processedQuestions.push(questionObj);
        }
    }

    // Sort by question number
    processedQuestions.sort((a, b) => {
        const numA = parseInt(a.numbers.match(/\d+/)[0]);
        const numB = parseInt(b.numbers.match(/\d+/)[0]);
        return numA - numB;
    });

    return processedQuestions;
}

function generateTypeScriptFile(questions) {
    let tsContent = `export type Option = {
  text?: string;
  image?: string;
};

export type Question = {
  numbers: string;
  question: Option[];
  options: Option[]; // options can be either text or an image object
  correctAnswer: number; // Index of the correct answer
  order: ("text" | "image")[];
};

export const quizData: Question[] = [
`;

    questions.forEach((question, index) => {
        tsContent += '  {\n';
        tsContent += `    numbers: "${question.numbers}",\n`;
        tsContent += '    question: [\n';
        
        question.question.forEach(q => {
            if (q.text) {
                tsContent += `      { text: "${q.text.replace(/"/g, '\\"')}" },\n`;
            } else if (q.image) {
                tsContent += `      { image: "${q.image}" },\n`;
            }
        });
        
        tsContent += '    ],\n';
        tsContent += '    options: [\n';
        
        question.options.forEach(opt => {
            tsContent += `      { text: "${opt.text.replace(/"/g, '\\"')}" },\n`;
        });
        
        tsContent += '    ],\n';
        tsContent += `    correctAnswer: ${question.correctAnswer},\n`;
        tsContent += `    order: ${JSON.stringify(question.order)},\n`;
        tsContent += '  }';
        
        if (index < questions.length - 1) {
            tsContent += ',';
        }
        tsContent += '\n';
    });

    tsContent += '];\n';
    
    return tsContent;
}

// Main execution
try {
    console.log('Extracting questions from HTML...');
    const questions = extractQuestionsFromHTML();
    
    console.log(`Found ${questions.length} questions`);
    
    // Show first few questions for verification
    questions.slice(0, 3).forEach((q, i) => {
        console.log(`\nQuestion ${i + 1}:`);
        console.log(`Number: ${q.numbers}`);
        console.log(`Text: ${q.question[0].text.substring(0, 80)}...`);
        console.log(`Options: ${q.options.length}`);
        console.log(`Correct: ${q.correctAnswer}`);
        if (q.question.length > 1) {
            console.log('Has image: Yes');
        }
    });
    
    const tsContent = generateTypeScriptFile(questions);
    fs.writeFileSync('testdriver_extracted.ts', tsContent, 'utf8');
    
    console.log('\nGenerated testdriver_extracted.ts successfully!');
    
} catch (error) {
    console.error('Error:', error.message);
}
